# Skyblock Outpost Event Countdown Timer

A Python-based countdown timer for Skyblock Outpost events that occur every 6 hours starting at 11:00 PM PST on July 29, 2025.

## Features

- **Accurate Timezone Handling**: Uses `pytz` for proper PST/PDT conversion including daylight saving time
- **6-Hour Event Cycle**: Calculates events at 11:00 PM, 5:00 AM, 11:00 AM, and 5:00 PM PST
- **Continuous Operation**: Updates every 60 seconds and runs indefinitely
- **File Output**: Writes countdown to `outpost_countdown.txt` for external integration
- **Comprehensive Logging**: Logs all updates and errors to `outpost_countdown.log`
- **Error Handling**: Robust error handling for timezone and file I/O operations

## Installation

1. **Install Python 3.7+** (if not already installed)

2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the countdown timer**:
   ```bash
   python outpost_countdown.py
   ```

## Usage

### Running the Timer

```bash
# Start the countdown timer
python outpost_countdown.py

# The timer will display:
# - Event schedule information
# - Current countdown status
# - Log updates every minute
```

### Output Files

- **`outpost_countdown.txt`**: Contains the current countdown (e.g., "Outpost: 2 Hours 34 Minutes")
- **`outpost_countdown.log`**: Contains detailed logs of all updates and errors

### Testing

Run the test script to verify countdown logic:

```bash
python test_countdown.py
```

## Event Schedule

Events occur every 6 hours in PST timezone:
- **11:00 PM PST** (23:00)
- **5:00 AM PST** (05:00)
- **11:00 AM PST** (11:00)
- **5:00 PM PST** (17:00)

The schedule automatically handles daylight saving time transitions.

## Integration with CustomHUD

⚠️ **Important**: CustomHUD cannot directly read external text files. To integrate this countdown with CustomHUD, you need one of these approaches:

### Option 1: Custom Minecraft Mod
Create a Forge/Fabric mod that:
1. Reads the `outpost_countdown.txt` file
2. Provides the countdown as a CustomHUD variable
3. Updates the variable when the file changes

### Option 2: Server Plugin Integration
If using a Minecraft server:
1. Server plugin reads the countdown file
2. Broadcasts countdown via chat or scoreboard
3. Use CustomHUD to display server-provided information

### Option 3: Manual Integration
1. Monitor the `outpost_countdown.txt` file
2. Manually copy the countdown text into CustomHUD configuration
3. Update CustomHUD profiles as needed

### Option 4: Overlay Application
Create a separate overlay application that:
1. Reads the countdown file
2. Displays countdown over Minecraft window
3. Independent of CustomHUD

## File Structure

```
customhud/
├── outpost_countdown.py      # Main countdown application
├── test_countdown.py         # Test script for validation
├── requirements.txt          # Python dependencies
├── README.md                # This file
├── outpost_countdown.txt    # Generated countdown output
└── outpost_countdown.log    # Generated log file
```

## Configuration

You can modify these constants in `outpost_countdown.py`:

```python
OUTPUT_FILE = "outpost_countdown.txt"    # Output file name
LOG_FILE = "outpost_countdown.log"       # Log file name
UPDATE_INTERVAL = 60                     # Update frequency (seconds)
EVENT_INTERVAL_HOURS = 6                 # Hours between events
```

## Troubleshooting

### Common Issues

1. **"pytz not found" error**:
   ```bash
   pip install pytz
   ```

2. **Permission errors writing files**:
   - Ensure the script has write permissions in the current directory
   - Run from a directory where you have write access

3. **Timezone issues**:
   - The script automatically handles PST/PDT transitions
   - Check system timezone if times seem incorrect

### Logging

Check `outpost_countdown.log` for detailed information about:
- Countdown calculations
- File write operations
- Error messages
- Timezone conversions

## Example Output

The countdown file will contain text like:
```
Outpost: 3 Hours 45 Minutes
Outpost: 1 Hour 23 Minutes
Outpost: 45 Minutes
Outpost: Less than 1 Minute
Outpost: Event Starting!
```

## Development

### Adding Features

To extend the countdown timer:

1. **Modify countdown format**: Edit the `format_countdown()` function
2. **Change update frequency**: Modify `UPDATE_INTERVAL`
3. **Add new output formats**: Extend `write_countdown_to_file()`
4. **Custom event schedules**: Modify the event calculation logic

### Testing

The test script validates:
- Countdown calculations across different times
- Event schedule accuracy
- Daylight saving time handling
- Edge cases and error conditions

## License

This code is provided as-is for educational and personal use. Modify as needed for your specific Skyblock server requirements.
