#!/usr/bin/env python3
"""
Setup script for CustomHUD integration

This script helps set up the countdown timer for CustomHUD integration by:
1. Detecting the correct CustomHUD directory
2. Creating necessary directories
3. Testing file permissions
4. Providing setup instructions
"""

import os
import sys
import platform
from pathlib import Path

def get_minecraft_directory():
    """Get the Minecraft directory based on the operating system."""
    system = platform.system()
    
    if system == "Windows":
        # Windows paths
        appdata = os.environ.get('APPDATA')
        if appdata:
            return Path(appdata) / ".minecraft"
        else:
            return Path.home() / "AppData" / "Roaming" / ".minecraft"
    
    elif system == "Darwin":  # macOS
        return Path.home() / "Library" / "Application Support" / "minecraft"
    
    elif system == "Linux":
        return Path.home() / ".minecraft"
    
    else:
        print(f"Unsupported operating system: {system}")
        return None

def get_customhud_profiles_directory():
    """Get the CustomHUD profiles directory."""
    minecraft_dir = get_minecraft_directory()
    if minecraft_dir:
        return minecraft_dir / "config" / "custom-hud" / "profiles"
    return None

def create_test_profile():
    """Create a test CustomHUD profile."""
    return """==Section:TopRight==
&6&lSkyblock Outpost
&aOutpost: Test Profile
&7This is a test profile
&7Generated by setup script

==Section:TopLeft==
&7Current Time: {real_time:HH:mm:ss}
&7System FPS: {fps}

==Section:BottomRight==
&8Setup Test Profile
&8{real_time:yyyy-MM-dd HH:mm:ss}
"""

def test_directory_access(directory):
    """Test if we can create and write to the directory."""
    try:
        # Create directory if it doesn't exist
        directory.mkdir(parents=True, exist_ok=True)
        
        # Test writing a file
        test_file = directory / "test_write.txt"
        with open(test_file, 'w') as f:
            f.write("Test write access")
        
        # Test reading the file
        with open(test_file, 'r') as f:
            content = f.read()
        
        # Clean up test file
        test_file.unlink()
        
        return True, "Success"
    
    except PermissionError:
        return False, "Permission denied - try running as administrator"
    except Exception as e:
        return False, f"Error: {e}"

def setup_customhud_integration():
    """Main setup function."""
    print("=" * 60)
    print("CUSTOMHUD OUTPOST COUNTDOWN SETUP")
    print("=" * 60)
    
    # Detect system
    system = platform.system()
    print(f"Operating System: {system}")
    
    # Get Minecraft directory
    minecraft_dir = get_minecraft_directory()
    print(f"Minecraft Directory: {minecraft_dir}")
    
    if not minecraft_dir:
        print("❌ Could not determine Minecraft directory")
        return False
    
    # Check if Minecraft directory exists
    if minecraft_dir.exists():
        print("✅ Minecraft directory found")
    else:
        print("❌ Minecraft directory not found")
        print("   Make sure Minecraft is installed")
        return False
    
    # Get CustomHUD profiles directory
    profiles_dir = get_customhud_profiles_directory()
    print(f"CustomHUD Profiles Directory: {profiles_dir}")
    
    # Test directory access
    print("\nTesting directory access...")
    can_write, message = test_directory_access(profiles_dir)
    
    if can_write:
        print("✅ Directory access successful")
        
        # Create the actual timer profile
        timer_file = profiles_dir / "timer.txt"
        try:
            with open(timer_file, 'w', encoding='utf-8') as f:
                f.write(create_test_profile())
            print(f"✅ Created test profile: {timer_file}")
            
            # Update the main script with the correct path
            update_main_script_path(str(timer_file))
            
            print("\n" + "=" * 60)
            print("SETUP COMPLETE!")
            print("=" * 60)
            print("Next steps:")
            print("1. Run: python outpost_countdown.py")
            print("2. In Minecraft: Mod Menu → CustomHud → Settings")
            print("3. Click 'Edit Profile 1/2/3'")
            print(f"4. Navigate to and select: {timer_file}")
            print("5. The countdown will appear in your HUD!")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create timer profile: {e}")
            return False
    else:
        print(f"❌ Directory access failed: {message}")
        
        # Suggest fallback
        print("\nFallback option:")
        print("The timer will create a local 'timer.txt' file instead.")
        print("You can manually copy this file to the CustomHUD profiles directory.")
        
        return False

def update_main_script_path(timer_path):
    """Update the main script with the correct timer path."""
    try:
        script_path = Path(__file__).parent / "outpost_countdown.py"
        
        # Read the current script
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the OUTPUT_FILE path
        old_line = r'DEFAULT_CUSTOMHUD_PATH = r"C:\Users\<USER>\AppData\Roaming\.minecraft\config\custom-hud\profiles\timer.txt"'
        new_line = f'DEFAULT_CUSTOMHUD_PATH = r"{timer_path}"'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Write back the updated script
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Updated main script with path: {timer_path}")
        else:
            print("⚠️  Could not automatically update main script path")
            print(f"   Manually update OUTPUT_FILE to: {timer_path}")
            
    except Exception as e:
        print(f"⚠️  Could not update main script: {e}")

if __name__ == "__main__":
    success = setup_customhud_integration()
    
    if not success:
        print("\nSetup encountered issues. The timer will use fallback mode.")
        print("Run 'python outpost_countdown.py' to start the timer anyway.")
    
    input("\nPress Enter to exit...")
