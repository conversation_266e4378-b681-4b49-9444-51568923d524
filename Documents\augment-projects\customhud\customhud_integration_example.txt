CustomHUD Integration Example
============================

Since CustomHUD cannot directly read external files, here are example configurations
that show how you might manually integrate the countdown information:

OPTION 1: Manual Static Display
===============================
Add this to your CustomHUD profile:

==Section:TopRight==
&6&lSkyblock Events
&7Next Outpost: &a[MANUAL UPDATE NEEDED]
&7Schedule: 11PM, 5AM, 11AM, 5PM PST
&7Current Time: {real_time:HH:mm}

OPTION 2: Time-Based Conditional Display
========================================
This shows different messages based on current time (approximate):

==Section:TopRight==
&6&lOutpost Events
=if: real_time:HH >= 23=
&aNext Event: Soon! (5:00 AM)
=elseif: real_time:HH >= 17=
&eNext Event: 11:00 PM PST
=elseif: real_time:HH >= 11=
&eNext Event: 5:00 PM PST
=elseif: real_time:HH >= 5=
&eNext Event: 11:00 AM PST
=else=
&eNext Event: 5:00 AM PST
=endif=
&7Current: {real_time:HH:mm} PST

OPTION 3: Real-Time Clock Display
=================================
Show current time for manual countdown calculation:

==Section:TopLeft==
&6&lTime Info
&7PST Time: {real_time:HH:mm:ss}
&7Outpost Events: Every 6 hours
&7Times: 23:00, 05:00, 11:00, 17:00

OPTION 4: Server Integration (If Available)
===========================================
If your server supports custom variables or plugins:

==Section:TopRight==
&6&lOutpost Countdown
&a{server_outpost_countdown}
&7Next Event: {server_next_outpost_time}

CUSTOM MOD INTEGRATION CONCEPT
==============================
For a custom mod that reads the countdown file, you would need:

1. A mod that reads "outpost_countdown.txt" every minute
2. Provides a CustomHUD variable like {outpost_countdown}
3. Usage in CustomHUD:

==Section:TopRight==
&6&lSkyblock Events
&a{outpost_countdown}
&7Updated every minute

MANUAL UPDATE WORKFLOW
======================
1. Run the Python countdown timer
2. Check outpost_countdown.txt file periodically
3. Copy the countdown text
4. Update your CustomHUD profile manually
5. Reload CustomHUD configuration

Example workflow:
- File contains: "Outpost: 2 Hours 34 Minutes"
- Update CustomHUD profile with: "&aOutpost: 2 Hours 34 Minutes"
- Reload CustomHUD to see changes
