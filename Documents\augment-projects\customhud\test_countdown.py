#!/usr/bin/env python3
"""
Test script for the Outpost Countdown Timer

This script tests the countdown logic with various scenarios to ensure
the timer calculates correctly across different times and dates.
"""

import datetime
import pytz
from outpost_countdown import get_next_event_time, format_countdown, PST_TIMEZONE, START_DATE

def test_countdown_logic():
    """Test the countdown calculation with various scenarios."""
    print("Testing Outpost Countdown Logic")
    print("=" * 40)
    
    # Test scenarios
    test_cases = [
        # Before start date
        datetime.datetime(2025, 7, 29, 20, 0, 0),  # 8:00 PM PST July 29
        
        # Right at start time
        datetime.datetime(2025, 7, 29, 23, 0, 0),  # 11:00 PM PST July 29
        
        # 1 hour after start
        datetime.datetime(2025, 7, 30, 0, 0, 0),   # 12:00 AM PST July 30
        
        # Just before second event
        datetime.datetime(2025, 7, 30, 4, 59, 0),  # 4:59 AM PST July 30
        
        # At second event
        datetime.datetime(2025, 7, 30, 5, 0, 0),   # 5:00 AM PST July 30
        
        # Mid-day
        datetime.datetime(2025, 7, 30, 14, 30, 0), # 2:30 PM PST July 30
        
        # Several days later
        datetime.datetime(2025, 8, 5, 16, 45, 0),  # 4:45 PM PST August 5
    ]
    
    for i, test_time in enumerate(test_cases, 1):
        # Localize test time to PST
        test_time_pst = PST_TIMEZONE.localize(test_time)
        
        # Calculate next event
        next_event = get_next_event_time(test_time_pst)
        
        # Calculate time remaining
        time_remaining = next_event - test_time_pst
        
        # Format countdown
        countdown_text = format_countdown(time_remaining)
        
        print(f"\nTest Case {i}:")
        print(f"  Current Time: {test_time_pst.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"  Next Event:   {next_event.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"  Countdown:    {countdown_text}")

def test_event_schedule():
    """Test that events occur at the correct 6-hour intervals."""
    print("\n" + "=" * 40)
    print("Testing Event Schedule")
    print("=" * 40)
    
    # Start from the initial event time
    start_time_pst = PST_TIMEZONE.localize(START_DATE)
    
    print("First 8 event times:")
    for i in range(8):
        event_time = start_time_pst + datetime.timedelta(hours=6*i)
        print(f"  Event {i+1}: {event_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")

def test_edge_cases():
    """Test edge cases like daylight saving time transitions."""
    print("\n" + "=" * 40)
    print("Testing Edge Cases")
    print("=" * 40)
    
    # Test around DST transition (example dates)
    dst_test_cases = [
        # Spring forward (March 2026 - example)
        datetime.datetime(2026, 3, 8, 1, 30, 0),   # Before DST
        datetime.datetime(2026, 3, 8, 3, 30, 0),   # After DST
        
        # Fall back (November 2025 - example)
        datetime.datetime(2025, 11, 2, 1, 30, 0),  # Before DST ends
        datetime.datetime(2025, 11, 2, 1, 30, 0),  # After DST ends
    ]
    
    for i, test_time in enumerate(dst_test_cases, 1):
        try:
            test_time_pst = PST_TIMEZONE.localize(test_time)
            next_event = get_next_event_time(test_time_pst)
            time_remaining = next_event - test_time_pst
            countdown_text = format_countdown(time_remaining)
            
            print(f"\nDST Test Case {i}:")
            print(f"  Current Time: {test_time_pst.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            print(f"  Next Event:   {next_event.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            print(f"  Countdown:    {countdown_text}")
        except Exception as e:
            print(f"\nDST Test Case {i}: Error - {e}")

if __name__ == "__main__":
    test_countdown_logic()
    test_event_schedule()
    test_edge_cases()
    
    print("\n" + "=" * 40)
    print("Testing Complete!")
    print("=" * 40)
