#!/usr/bin/env python3
"""
Skyblock Outpost Event Countdown Timer

This application calculates and maintains a countdown to the next Skyblock Outpost event.
Events occur every 6 hours starting at 11:00 PM PST on July 29, 2025.

The countdown is written to a text file that can be read by external applications.
While CustomHUD cannot directly read external files, this creates the foundation
for integration with custom mods or server plugins.

Author: Generated for CustomHUD Integration
Date: 2025-07-30
"""

import datetime
import pytz
import time
import logging
import os
import sys
from pathlib import Path

# Configuration
# Default CustomHUD profile path - modify this to match your Minecraft installation
DEFAULT_CUSTOMHUD_PATH = r"C:\Users\<USER>\AppData\Roaming\.minecraft\config\custom-hud\profiles\timer.txt"
# Fallback to local directory if CustomHUD directory doesn't exist
LOCAL_FALLBACK_PATH = "timer.txt"

OUTPUT_FILE = DEFAULT_CUSTOMHUD_PATH
LOG_FILE = "outpost_countdown.log"
UPDATE_INTERVAL = 60  # seconds
EVENT_INTERVAL_HOURS = 6

# Event schedule starts July 29, 2025 at 11:00 PM PST
START_DATE = datetime.datetime(2025, 7, 29, 23, 0, 0)  # 11:00 PM
PST_TIMEZONE = pytz.timezone('US/Pacific')

def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler(sys.stdout)
        ]
    )

def get_next_event_time(current_time_pst):
    """
    Calculate the next Outpost event time based on the 6-hour recurring schedule.
    
    Args:
        current_time_pst: Current time in PST timezone
        
    Returns:
        datetime: Next event time in PST
    """
    # Localize the start date to PST
    start_time_pst = PST_TIMEZONE.localize(START_DATE)
    
    # Calculate how much time has passed since the start
    time_since_start = current_time_pst - start_time_pst
    
    # If we're before the start date, return the start date
    if time_since_start.total_seconds() < 0:
        return start_time_pst
    
    # Calculate how many complete 6-hour intervals have passed
    interval_seconds = EVENT_INTERVAL_HOURS * 3600
    intervals_passed = int(time_since_start.total_seconds() // interval_seconds)
    
    # Calculate the next event time
    next_event = start_time_pst + datetime.timedelta(seconds=(intervals_passed + 1) * interval_seconds)
    
    return next_event

def format_countdown(time_remaining):
    """
    Format the time remaining into the required format.
    
    Args:
        time_remaining: timedelta object representing time until next event
        
    Returns:
        str: Formatted countdown string
    """
    total_seconds = int(time_remaining.total_seconds())
    
    if total_seconds <= 0:
        return "Outpost: Event Starting!"
    
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    
    # Handle singular vs plural
    hour_text = "Hour" if hours == 1 else "Hours"
    minute_text = "Minute" if minutes == 1 else "Minutes"
    
    if hours > 0 and minutes > 0:
        return f"Outpost: {hours} {hour_text} {minutes} {minute_text}"
    elif hours > 0:
        return f"Outpost: {hours} {hour_text}"
    elif minutes > 0:
        return f"Outpost: {minutes} {minute_text}"
    else:
        return "Outpost: Less than 1 Minute"

def create_customhud_profile(countdown_text):
    """
    Create a complete CustomHUD profile configuration with the countdown.

    Args:
        countdown_text: The formatted countdown string

    Returns:
        str: Complete CustomHUD profile configuration
    """
    profile_content = f"""==Section:TopRight==
&6&lSkyblock Outpost
&a{countdown_text}
&7Updated every minute
&7Events: 11PM, 5AM, 11AM, 5PM PST

==Section:TopLeft==
&7Current Time: {{real_time:HH:mm:ss}}
&7System FPS: {{fps}}

==Section:BottomRight==
&8Generated by Outpost Timer
&8{{real_time:yyyy-MM-dd HH:mm:ss}}
"""
    return profile_content

def write_countdown_to_file(countdown_text):
    """
    Write the countdown as a complete CustomHUD profile with error handling.
    Falls back to local directory if CustomHUD directory is not accessible.

    Args:
        countdown_text: The formatted countdown string to write
    """
    global OUTPUT_FILE

    try:
        # Ensure the directory exists
        profile_dir = os.path.dirname(OUTPUT_FILE)
        os.makedirs(profile_dir, exist_ok=True)

        # Create the complete CustomHUD profile
        profile_content = create_customhud_profile(countdown_text)

        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write(profile_content)
            f.flush()  # Ensure data is written immediately
        logging.debug(f"Successfully wrote CustomHUD profile: {countdown_text}")

    except (PermissionError, FileNotFoundError, OSError) as e:
        # Fall back to local directory if CustomHUD directory is not accessible
        if OUTPUT_FILE != LOCAL_FALLBACK_PATH:
            logging.warning(f"Cannot write to CustomHUD directory: {e}")
            logging.warning(f"Falling back to local file: {LOCAL_FALLBACK_PATH}")
            OUTPUT_FILE = LOCAL_FALLBACK_PATH

            try:
                profile_content = create_customhud_profile(countdown_text)
                with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                    f.write(profile_content)
                    f.flush()
                logging.info(f"Successfully wrote to fallback file: {OUTPUT_FILE}")
            except Exception as fallback_error:
                logging.error(f"Failed to write to fallback file: {fallback_error}")
        else:
            logging.error(f"Failed to write to file {OUTPUT_FILE}: {e}")
    except Exception as e:
        logging.error(f"Unexpected error writing to file: {e}")

def calculate_outpost_countdown():
    """
    Main function to calculate countdown and update the output file.
    """
    try:
        # Get current time in PST
        pst = pytz.timezone('US/Pacific')
        now_utc = datetime.datetime.now(pytz.UTC)
        now_pst = now_utc.astimezone(pst)
        
        # Calculate next event time
        next_event = get_next_event_time(now_pst)
        
        # Calculate time remaining
        time_remaining = next_event - now_pst
        
        # Format the countdown
        countdown_text = format_countdown(time_remaining)
        
        # Write to file
        write_countdown_to_file(countdown_text)
        
        # Log the update
        logging.info(f"Updated CustomHUD profile: {countdown_text}")
        logging.debug(f"Current PST time: {now_pst.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Next event time: {next_event.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Profile written to: {OUTPUT_FILE}")
        
    except pytz.exceptions.UnknownTimeZoneError as e:
        logging.error(f"Timezone error: {e}")
    except Exception as e:
        logging.error(f"Unexpected error in countdown calculation: {e}")

def print_event_schedule():
    """Print the event schedule for reference."""
    print("\n" + "="*60)
    print("SKYBLOCK OUTPOST EVENT COUNTDOWN - CUSTOMHUD INTEGRATION")
    print("="*60)
    print("Events occur every 6 hours starting July 29, 2025:")
    print("• 11:00 PM PST")
    print("• 5:00 AM PST")
    print("• 11:00 AM PST")
    print("• 5:00 PM PST")
    print("="*60)
    print(f"CustomHUD Profile: {OUTPUT_FILE}")
    print(f"Log file: {LOG_FILE}")
    print(f"Update interval: {UPDATE_INTERVAL} seconds")
    print("="*60)
    print("USAGE INSTRUCTIONS:")
    print("1. Make sure this timer is running")
    print("2. In Minecraft, open Mod Menu → CustomHud → Settings")
    print("3. Click 'Edit Profile 1/2/3' and select the timer.txt file")
    print("4. The countdown will appear in your HUD automatically!")
    print("="*60 + "\n")

def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    
    # Print schedule information
    print_event_schedule()
    
    logging.info("Starting Skyblock Outpost Countdown Timer - CustomHUD Integration")
    logging.info(f"CustomHUD Profile: {OUTPUT_FILE}")
    logging.info(f"Update interval: {UPDATE_INTERVAL} seconds")
    
    # Initial countdown calculation
    calculate_outpost_countdown()
    
    try:
        # Main loop
        while True:
            time.sleep(UPDATE_INTERVAL)
            calculate_outpost_countdown()
            
    except KeyboardInterrupt:
        logging.info("Countdown timer stopped by user")
        print("\nCountdown timer stopped.")
    except Exception as e:
        logging.error(f"Fatal error in main loop: {e}")
        print(f"Fatal error: {e}")
    finally:
        # Write final message to profile
        try:
            write_countdown_to_file("Timer Stopped")
        except:
            pass

if __name__ == "__main__":
    main()
